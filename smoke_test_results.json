{"timestamp": "2025-07-29T22:59:42.894463", "tests": {"connectivity": {"success": false, "message": "API connectivity issue. Status: 400, Response time: 1172.66ms"}, "authentication": {"success": false, "message": "Authentication failed. Status: 401, Response: {\"error\":\"unauthorized_client\",\"error_description\":\"Invalid client ID or secret, or client not subscribed to this API\"}"}, "token_validity": {"success": false, "message": "Skipped due to authentication failure"}}, "overall_success": false, "summary": {"total_tests": 3, "passed_tests": 0, "failed_tests": 3, "success_rate": "0.0%"}}