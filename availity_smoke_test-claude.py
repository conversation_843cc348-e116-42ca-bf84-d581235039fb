#!/usr/bin/env python3
"""
Availity API Authentication Smoke Test
=====================================

This script performs a comprehensive authentication smoke test for the Availity API.
It validates credentials, obtains access tokens, and tests basic API connectivity.

Author: Generated for Availity API Testing
Date: July 29, 2025
"""

import os
import sys
import time
import json
import logging
import requests
from datetime import datetime, timedelta
from dotenv import load_dotenv
from typing import Dict, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('availity_smoke_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class AvailityAuthSmokeTest:
    """
    Availity API Authentication Smoke Test Class
    
    This class handles all aspects of testing authentication with the Availity API,
    including token acquisition, validation, and basic connectivity tests.
    """
    
    def __init__(self):
        """Initialize the smoke test with configuration from environment variables."""
        load_dotenv()
        
        # Load configuration from environment
        self.api_key = os.getenv('AVAILITY_API_KEY')
        self.api_secret = os.getenv('AVAILITY_API_SECRET')
        self.base_url = os.getenv('AVAILITY_BASE_URL', 'https://api.availity.com')
        self.auth_url = os.getenv('AVAILITY_AUTH_URL', 'https://api.availity.com/availity/v1/token')
        
        # Validate required configuration
        self._validate_config()
        
        # Initialize session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Availity-Auth-SmokeTest/1.0',
            'Accept': 'application/json',
            'Content-Type': 'application/x-www-form-urlencoded'
        })
        
        # Token storage
        self.access_token = None
        self.token_expires_at = None
        
        logger.info("Availity Auth Smoke Test initialized successfully")
    
    def _validate_config(self) -> None:
        """Validate that all required configuration is present."""
        missing_configs = []
        
        if not self.api_key:
            missing_configs.append('AVAILITY_API_KEY')
        if not self.api_secret:
            missing_configs.append('AVAILITY_API_SECRET')
        
        if missing_configs:
            error_msg = f"Missing required environment variables: {', '.join(missing_configs)}"
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("Configuration validation passed")
    
    def authenticate(self) -> Tuple[bool, str]:
        """
        Authenticate with the Availity API using client credentials flow.
        
        Returns:
            Tuple[bool, str]: (success_status, message)
        """
        logger.info("Starting authentication process...")
        
        try:
            # Prepare authentication payload
            auth_payload = {
                'grant_type': 'client_credentials',
                'client_id': self.api_key,
                'client_secret': self.api_secret,
                'scope': 'hipaa'  # Standard scope for Availity API
            }
            
            logger.debug(f"Sending authentication request to: {self.auth_url}")
            
            # Make authentication request
            response = self.session.post(
                self.auth_url,
                data=auth_payload,
                timeout=30
            )
            
            # Log response details
            logger.debug(f"Auth response status: {response.status_code}")
            logger.debug(f"Auth response headers: {dict(response.headers)}")
            
            if response.status_code == 200:
                token_data = response.json()
                
                # Extract token information
                self.access_token = token_data.get('access_token')
                expires_in = token_data.get('expires_in', 3600)  # Default 1 hour
                token_type = token_data.get('token_type', 'Bearer')
                
                # Calculate expiration time
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                # Update session headers with token
                self.session.headers.update({
                    'Authorization': f'{token_type} {self.access_token}'
                })
                
                success_msg = f"Authentication successful. Token expires at: {self.token_expires_at}"
                logger.info(success_msg)
                
                return True, success_msg
            
            else:
                error_msg = f"Authentication failed. Status: {response.status_code}, Response: {response.text}"
                logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "Authentication request timed out"
            logger.error(error_msg)
            return False, error_msg
        
        except requests.exceptions.ConnectionError:
            error_msg = "Failed to connect to Availity API"
            logger.error(error_msg)
            return False, error_msg
        
        except requests.exceptions.RequestException as e:
            error_msg = f"Authentication request failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        
        except json.JSONDecodeError:
            error_msg = "Invalid JSON response from authentication endpoint"
            logger.error(error_msg)
            return False, error_msg
        
        except Exception as e:
            error_msg = f"Unexpected error during authentication: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def test_token_validity(self) -> Tuple[bool, str]:
        """
        Test if the current access token is valid by making a test API call.
        
        Returns:
            Tuple[bool, str]: (success_status, message)
        """
        if not self.access_token:
            return False, "No access token available. Please authenticate first."
        
        logger.info("Testing token validity...")
        
        try:
            # Test endpoint - typically a lightweight endpoint that requires authentication
            test_url = f"{self.base_url}/availity/v1/configurations"
            
            response = self.session.get(test_url, timeout=30)
            
            if response.status_code == 200:
                success_msg = "Token validation successful - API access confirmed"
                logger.info(success_msg)
                return True, success_msg
            
            elif response.status_code == 401:
                error_msg = "Token validation failed - Authentication required"
                logger.error(error_msg)
                return False, error_msg
            
            elif response.status_code == 403:
                error_msg = "Token validation failed - Access forbidden"
                logger.error(error_msg)
                return False, error_msg
            
            else:
                error_msg = f"Token validation inconclusive. Status: {response.status_code}"
                logger.warning(error_msg)
                return False, error_msg
                
        except requests.exceptions.RequestException as e:
            error_msg = f"Token validation request failed: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
        
        except Exception as e:
            error_msg = f"Unexpected error during token validation: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def test_api_connectivity(self) -> Tuple[bool, str]:
        """
        Test basic API connectivity and responsiveness.
        
        Returns:
            Tuple[bool, str]: (success_status, message)
        """
        logger.info("Testing API connectivity...")
        
        try:
            # Test basic connectivity without authentication first
            health_check_url = f"{self.base_url}/availity/v1/configurations"  # Public endpoint
            
            start_time = time.time()
            response = self.session.get(health_check_url, timeout=30)
            response_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            if response.status_code in [200, 401, 403]:  # Any of these indicates the API is responding
                success_msg = f"API connectivity successful. Response time: {response_time:.2f}ms"
                logger.info(success_msg)
                return True, success_msg
            
            else:
                error_msg = f"API connectivity issue. Status: {response.status_code}, Response time: {response_time:.2f}ms"
                logger.error(error_msg)
                return False, error_msg
                
        except requests.exceptions.Timeout:
            error_msg = "API connectivity test timed out"
            logger.error(error_msg)
            return False, error_msg
        
        except requests.exceptions.ConnectionError:
            error_msg = "Failed to connect to Availity API"
            logger.error(error_msg)
            return False, error_msg
        
        except Exception as e:
            error_msg = f"Unexpected error during connectivity test: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def run_comprehensive_smoke_test(self) -> Dict[str, any]:
        """
        Run a comprehensive smoke test including all validation steps.
        
        Returns:
            Dict containing test results and summary
        """
        logger.info("=" * 50)
        logger.info("STARTING AVAILITY API COMPREHENSIVE SMOKE TEST")
        logger.info("=" * 50)
        
        test_results = {
            'timestamp': datetime.now().isoformat(),
            'tests': {},
            'overall_success': False,
            'summary': {}
        }
        
        # Test 1: API Connectivity
        logger.info("\n1. Testing API Connectivity...")
        connectivity_success, connectivity_msg = self.test_api_connectivity()
        test_results['tests']['connectivity'] = {
            'success': connectivity_success,
            'message': connectivity_msg
        }
        
        # Test 2: Authentication
        logger.info("\n2. Testing Authentication...")
        auth_success, auth_msg = self.authenticate()
        test_results['tests']['authentication'] = {
            'success': auth_success,
            'message': auth_msg
        }
        
        # Test 3: Token Validity (only if authentication succeeded)
        if auth_success:
            logger.info("\n3. Testing Token Validity...")
            token_success, token_msg = self.test_token_validity()
            test_results['tests']['token_validity'] = {
                'success': token_success,
                'message': token_msg
            }
        else:
            logger.info("\n3. Skipping Token Validity Test (Authentication failed)")
            test_results['tests']['token_validity'] = {
                'success': False,
                'message': 'Skipped due to authentication failure'
            }
            token_success = False
        
        # Calculate overall success
        all_tests_passed = all(test['success'] for test in test_results['tests'].values())
        test_results['overall_success'] = all_tests_passed
        
        # Generate summary
        passed_tests = sum(1 for test in test_results['tests'].values() if test['success'])
        total_tests = len(test_results['tests'])
        
        test_results['summary'] = {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'failed_tests': total_tests - passed_tests,
            'success_rate': f"{(passed_tests/total_tests)*100:.1f}%"
        }
        
        # Log final results
        logger.info("\n" + "=" * 50)
        logger.info("SMOKE TEST RESULTS SUMMARY")
        logger.info("=" * 50)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {test_results['summary']['success_rate']}")
        logger.info(f"Overall Status: {'PASS' if all_tests_passed else 'FAIL'}")
        
        if all_tests_passed:
            logger.info("\n✅ All tests passed! Your Availity API integration is working correctly.")
        else:
            logger.error("\n❌ Some tests failed. Please review the errors above.")
        
        return test_results
    
    def cleanup(self):
        """Clean up resources."""
        if self.session:
            self.session.close()
        logger.info("Cleanup completed")

def main():
    """Main execution function."""
    smoke_test = None
    
    try:
        # Initialize and run smoke test
        smoke_test = AvailityAuthSmokeTest()
        results = smoke_test.run_comprehensive_smoke_test()
        
        # Save results to file
        with open('smoke_test_results.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        logger.info(f"\nDetailed results saved to: smoke_test_results.json")
        
        # Exit with appropriate code
        sys.exit(0 if results['overall_success'] else 1)
        
    except KeyboardInterrupt:
        logger.info("\nSmoke test interrupted by user")
        sys.exit(130)
    
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        sys.exit(1)
    
    finally:
        if smoke_test:
            smoke_test.cleanup()

if __name__ == "__main__":
    main()
